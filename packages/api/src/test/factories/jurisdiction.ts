import { lazy } from "@skywind-group/sw-utils";
import { get, getEntityJurisdictionModel } from "../../skywind/models/jurisdiction";
import { UserBuildOptions } from "./user";
import { EntityBuildOptions } from "./entity";
import { FACTORY } from "./common";
import { AllowedJackpotConfigurationLevel } from "../../skywind/entities/jurisdiction";

const FactoryGirl = require("factory-girl");
const jurisdictionModel = get();
const entityJurisdictionModel = getEntityJurisdictionModel();

interface JurisdictionBuildOptions {
    title?: string;
    createdUserId?: number;
    updatedUserId?: number;
    description?: string;
    settings?: { [field: string]: string };
    createdAt?: Date;
    updatedAt?: Date;
    userBuildOptions?: UserBuildOptions;
    allowedCountries?: string[];
    restrictedCountries?: string[];
    defaultCountry?: string;
    allowedJackpotConfigurationLevel?: AllowedJackpotConfigurationLevel;
}

interface EntityJurisdictionBuildOptions {
    jurisdictionId?: number;
    entityId?: number;
    jurisdictionBuildOptions?: JurisdictionBuildOptions;
    entityBuildOptions?: EntityBuildOptions;
}

export const defineJurisdictionFactory = lazy(() => {

    FactoryGirl.factory.define(FACTORY.JURISDICTION, jurisdictionModel, (buildOptions: JurisdictionBuildOptions = {}) => {
        const { userBuildOptions, ...options} = buildOptions || {};

        const attrs = {
            title: FactoryGirl.factory.chance("word"),
            description: FactoryGirl.factory.chance("sentence"),
            code: FactoryGirl.factory.chance("word", { length: 6 }),
            settings: {
                showRTP: true,
                rulesDateStamped: true
            },
            ...options
        };

        if (!attrs.createdUserId) {
            attrs.createdUserId = FactoryGirl.factory.assoc(FACTORY.USER, "id", {}, userBuildOptions || {});
        }

        return attrs;
    });

    return FactoryGirl.factory.define(FACTORY.ENTITY_JURISDICTION, entityJurisdictionModel,
        (buildOptions: EntityJurisdictionBuildOptions = {}) => {
            const attrs = {
                jurisdictionId: buildOptions.jurisdictionId,
                entityId: buildOptions.entityId
            };

            if (!attrs.jurisdictionId) {
                attrs.jurisdictionId = FactoryGirl.factory.assoc(FACTORY.JURISDICTION, "id", {},
                    buildOptions.jurisdictionBuildOptions || {});
            }

            if (!attrs.entityId) {
                attrs.entityId = FactoryGirl.factory.assoc(
                    FACTORY.ENTITY, "id", {}, buildOptions.entityBuildOptions || {});
            }
            return attrs;
        });
});
