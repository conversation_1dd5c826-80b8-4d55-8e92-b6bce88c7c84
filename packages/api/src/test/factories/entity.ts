import { get as getEntityModel } from "../../skywind/models/entity";
import { lazy } from "@skywind-group/sw-utils";
import { BaseEntity, ChildEntity, Entity, ENTITY_TYPE, EntityStatus } from "../../skywind/entities/entity";
import { findOne } from "../../skywind/services/entity";
import { FACTORY } from "./common";
import EntityCache from "../../skywind/cache/entity";

const FactoryGirl = require("factory-girl");
const entityModel = getEntityModel();

export interface EntityBuildOptions {
    type?: string;
    name?: string;
    title?: string;
    description?: string;
    status?: string;
    key?: string;
    defaultCurrency?: string;
    defaultCountry?: string;
    defaultLanguage?: string;
    countries?: string[];
    currencies?: string[];
    languages?: string[];
    version?: number;
    parent?: BaseEntity;
}

export const defineEntityFactory = lazy(() => {
    FactoryGirl.factory.define(FACTORY.ENTITY, entityModel, (buildOptions: EntityBuildOptions = {}) => {
        const name = FactoryGirl.factory.chance("guid")();
        const { parent, ...options } = buildOptions || {};
        const parentId = parent ? parent.id : 1; // default parent is master
        const parentPath = parent ? parent.path : ":";

        const attrs = {
            type: ENTITY_TYPE.ENTITY,
            name: name,
            title: FactoryGirl.factory.chance("word"),
            description: FactoryGirl.factory.chance("sentence"),
            status: EntityStatus.NORMAL,
            key: FactoryGirl.factory.chance("guid"),
            defaultCurrency: "USD",
            defaultCountry: "US",
            defaultLanguage: "en",
            countries: ["US"],
            currencies: ["BNS", "USD"],
            languages: ["en"],
            version: 0,
            ...options,
            parent: parentId, // Ensure parent is always the ID, not the object
        };

        return {
            path: `${parentPath}${attrs.name}:`,
            ...attrs
        };
    }, {
        afterCreate

    });

    FactoryGirl.factory.extend(FACTORY.ENTITY, FACTORY.BRAND, {
        type: ENTITY_TYPE.BRAND
    }, {
        afterCreate
    });

    return FactoryGirl.factory.extend(FACTORY.ENTITY, FACTORY.MERCHANT_ENTITY, {
        type: ENTITY_TYPE.MERCHANT
    }, {
        afterCreate
    });

});

export const decorateGetAttributeMethod = (item) => {
    // hack for use association - FactoryGirl.factory.assoc("Entity", "id",...)
    item.get = name => item[name];
    return item;
};

const afterCreate = async (model, attrs, buildOptions: EntityBuildOptions = {}) => {
    EntityCache.reset();
    // return EntityImpl because we need all logic with parent and etc.
    const entity = await findOne({
        id: model.id
    });

    if (buildOptions && buildOptions.parent) {
        // add to parent created entity
        (buildOptions.parent as Entity).child.push(entity as ChildEntity);
    }

    return decorateGetAttributeMethod(entity);
};
