import { suite, test } from "mocha-typescript";
import { expect } from "chai";
import { BrandEntity } from "../../skywind/entities/brand";
import { validatePlaymode } from "../../skywind/utils/validatePlaymode";
import { Merchant } from "../../skywind/entities/merchant";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";

@suite
class ValidatePlaymodeSpec {
    public brandMock: BrandEntity = { isMerchant: false } as any;
    public brandMerchantMock: BrandEntity = { isMerchant: true } as any;
    public merchantSupportPlayMoneyMock: Merchant = { params: { supportPlayMoney: true }} as any;
    public merchantNotSupportedPlayMoneyMock: Merchant = { params: { supportPlayMoney: false }} as any;

    @test
    public realModeSuccessfullyPassed() {
        expect(validatePlaymode(this.brandMock, PlayMode.REAL)).to.be.true;
        expect(validatePlaymode(this.brandMerchantMock, PlayMode.REAL, this.merchantSupportPlayMoneyMock)).to.be.true;
    }

    @test
    public funModeSuccessfullyPassed() {
        expect(validatePlaymode(this.brandMock, PlayMode.FUN)).to.be.true;
        expect(validatePlaymode(this.brandMerchantMock, PlayMode.FUN, this.merchantSupportPlayMoneyMock)).to.be.true;
    }

    @test
    public playmoneySuccessfullyPassed() {
        expect(validatePlaymode(this.brandMerchantMock, PlayMode.PLAY_MONEY,
            this.merchantSupportPlayMoneyMock)).to.be.true;
    }

    @test
    public playmoneyFailed() {
        expect(() => validatePlaymode(this.brandMock, PlayMode.PLAY_MONEY)).to.throw();
        expect(() => validatePlaymode(this.brandMerchantMock, PlayMode.PLAY_MONEY,
            this.merchantNotSupportedPlayMoneyMock)).to.throw();
    }
}
