{"name": "@skywind-group/sw-management-playservice", "version": "2.137.0", "description": "", "license": "ISC", "author": "<PERSON> <<EMAIL>>", "main": "lib/index.js", "typings": "lib/index.d.ts", "scripts": {"clean": "rm -rf ./lib", "compile": "tsc -b tsconfig.build.json", "version": "mkdir -p lib/skywind && echo $(node -p \"require('./package.json').version\") $( git log --pretty=format:'%h' -n 1) $(date) > ./lib/skywind/version"}, "dependencies": {"@skywind-group/sw-currency-exchange": "2.3.12", "@skywind-group/sw-deferred-payment": "^2.0.0", "@skywind-group/sw-management-promo-wallet": "~2.137.0", "@skywind-group/sw-management-wallet": "~2.137.0", "@skywind-group/sw-wallet": "1.0.3", "@skywind-group/sw-wallet-adapter-core": "2.1.3", "lodash": "4.17.21"}}