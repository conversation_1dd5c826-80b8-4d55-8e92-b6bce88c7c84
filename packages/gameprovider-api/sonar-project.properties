sonar.projectKey=sw-management-gameprovider-api
sonar.projectName=sw-management-gameprovider-api
sonar.projectVersion=5.53
sonar.sourceEncoding=UTF-8
sonar.sources=src/skywind
sonar.exclusions=**/node_modules,src/test,src/skywind/app.ts,src/skywind/appGameProvider.ts,src/skywind/appOperator.ts,src/skywind/appPlayer.ts,src/skywind/appReport.ts,src/skywind/appSite.ts,src/skywind/appTerminal.ts,src/skywind/serverOperator.ts,src/skywind/serverPlayer.ts,src/skywind/serverReport.ts,src/skywind/serverTerminal.ts,src/skywind/api/routersPlayer.ts,src/skywind/api/routersReport.ts,src/skywind/api/routersTerminal.ts,src/skywind/services/externalreference.ts,src/skywind/api/playerAsync.ts,src/skywind/api/terminalAsync.ts
sonar.tests=src/test

sonar.language=ts
sonar.typescript.lcov.reportPaths=coverage/lcov.lcov
sonar.typescript.tsconfig=tsconfig.service.json
